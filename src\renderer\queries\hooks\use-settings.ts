import type { Settings } from "@shared/triplit/types";

import { settingsQueries } from "../definitions/settings-queries";
import type { QueryConfig, QueryOneConfig, QueryResult } from "../types";
import { useStandardQuery, useStandardQueryOne } from "./use-standard-query";

/**
 * 获取所有settings记录
 */
export function useSettings(
  config?: QueryConfig<"settings">,
): QueryResult<Settings[]> {
  return useStandardQuery(settingsQueries.all, config);
}

/**
 * 获取第一个(主要的)settings记录
 */
export function useSettingsOne(
  config?: QueryOneConfig<"settings">,
): QueryResult<Settings | null> {
  return useStandardQueryOne(settingsQueries.all, config);
}

/**
 * 获取特定的settings值
 */
export function useSettingsValue<K extends keyof Settings>(
  key: K,
  config?: QueryConfig<"settings", Settings[K] | undefined>,
): QueryResult<Settings[K] | undefined> {
  return useStandardQuery(settingsQueries.all, {
    ...config,
    select: (data) => data?.[0]?.[key],
  });
}

/**
 * 获取主题设置
 */
export function useThemeSetting(
  config?: QueryConfig<"settings", Settings["theme"] | undefined>,
) {
  return useSettingsValue("theme", config);
}

/**
 * 获取语言设置
 */
export function useLanguageSetting(
  config?: QueryConfig<"settings", Settings["language"] | undefined>,
) {
  return useSettingsValue("language", config);
}

/**
 * 获取隐私模式设置
 */
export function usePrivacySetting(
  config?: QueryConfig<"settings", Settings["isPrivate"] | undefined>,
) {
  return useSettingsValue("isPrivate", config);
}

/**
 * 获取选中的模型ID
 */
export function useSelectedModelId(
  config?: QueryConfig<"settings", Settings["selectedModelId"] | undefined>,
) {
  return useSettingsValue("selectedModelId", config);
}

/**
 * 获取新聊天模型ID设置
 */
export function useNewChatModelId(
  config?: QueryConfig<"settings", Settings["newChatModelId"] | undefined>,
) {
  return useSettingsValue("newChatModelId", config);
}

/**
 * 获取流式输出平滑器设置
 */
export function useStreamSmootherSetting(
  config?: QueryConfig<
    "settings",
    Settings["streamSmootherEnabled"] | undefined
  >,
) {
  return useSettingsValue("streamSmootherEnabled", config);
}

/**
 * 获取搜索服务设置
 */
export function useSearchServiceSetting(
  config?: QueryConfig<"settings", Settings["searchService"] | undefined>,
) {
  return useSettingsValue("searchService", config);
}

/**
 * 获取是否显示应用商店设置
 */
export function useDisplayAppStoreSetting(
  config?: QueryConfig<"settings", Settings["displayAppStore"] | undefined>,
) {
  return useSettingsValue("displayAppStore", config);
}

/**
 * 获取默认隐私模式设置
 */
export function useDefaultPrivacyModeSetting(
  config?: QueryConfig<"settings", Settings["defaultPrivacyMode"] | undefined>,
) {
  return useSettingsValue("defaultPrivacyMode", config);
}

/**
 * 获取流式输出速度设置
 */
export function useStreamSpeedSetting(
  config?: QueryConfig<"settings", Settings["streamSpeed"] | undefined>,
) {
  return useSettingsValue("streamSpeed", config);
}

/**
 * 获取流式输出相关设置
 */
export function useStreamOutputSettings() {
  const { data: streamSmootherEnabled } = useStreamSmootherSetting();
  const { data: streamSpeed } = useStreamSpeedSetting();

  return {
    streamSmootherEnabled: streamSmootherEnabled ?? true,
    streamSpeed,
  };
}

/**
 * 获取聊天相关设置
 */
export function useChatSettings() {
  const { data: collapseCodeBlock } = useSettingsValue("collapseCodeBlock");
  const { data: hideReason } = useSettingsValue("hideReason");
  const { data: collapseThinkBlock } = useSettingsValue("collapseThinkBlock");
  const { data: disableMarkdown } = useSettingsValue("disableMarkdown");

  return {
    collapseCodeBlock: collapseCodeBlock ?? false,
    hideReason: hideReason ?? false,
    collapseThinkBlock: collapseThinkBlock ?? false,
    disableMarkdown: disableMarkdown ?? false,
  };
}

/**
 * 获取网络搜索设置
 */
export function useWebSearchSetting(
  config?: QueryConfig<"settings", Settings["enableWebSearch"] | undefined>,
) {
  return useSettingsValue("enableWebSearch", config);
}

/**
 * 获取推理功能设置
 */
export function useReasonSetting(
  config?: QueryConfig<"settings", Settings["enableReason"] | undefined>,
) {
  return useSettingsValue("enableReason", config);
}

/**
 * 获取聊天工具栏相关设置
 */
export function useChatToolbarSettings() {
  const { data: enableWebSearch } = useWebSearchSetting();
  const { data: enableReason } = useReasonSetting();

  return {
    enableWebSearch: enableWebSearch ?? false,
    enableReason: enableReason ?? false,
  };
}

/**
 * 获取自动更新设置
 */
export function useAutoUpdateSetting(
  config?: QueryConfig<"settings", Settings["autoUpdate"] | undefined>,
) {
  return useSettingsValue("autoUpdate", config);
}

/**
 * 获取标题模型ID设置
 */
export function useTitleModelIdSetting(
  config?: QueryConfig<"settings", Settings["titleModelId"] | undefined>,
) {
  return useSettingsValue("titleModelId", config);
}

/**
 * 获取标题模型切换设置
 */
export function useTitleModelToggleSetting(
  config?: QueryConfig<"settings", Settings["titleModelToggle"] | undefined>,
) {
  return useSettingsValue("titleModelToggle", config);
}
