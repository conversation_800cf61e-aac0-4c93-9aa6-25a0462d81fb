import { Label } from "@renderer/components/ui/field";
import { Select } from "@renderer/components/ui/select";
import { cn } from "@renderer/lib/utils";
import { useTitleModelToggleSetting } from "@renderer/queries/hooks/use-settings";
import type { Key } from "react-aria-components";
import { useTranslation } from "react-i18next";

const { settingsService } = window.service;

export function TitleModelToggle() {
  const { t } = useTranslation("translation", {
    keyPrefix: "settings.preference-settings.title-model-toggle",
  });

  const titleModelToggles = [
    { key: "first-conversation", label: t("first-conversation") },
    { key: "every-conversation", label: t("every-conversation") },
    { key: "off", label: t("off") },
  ];

  const { data: currentTitleModelToggle } = useTitleModelToggleSetting();
  const titleModelToggle = currentTitleModelToggle ?? "first-conversation";

  const handleTitleModelToggleChange = async (key: Key | null) => {
    console.log("asdasdasd", key);

    await settingsService.setTitleModelToggle(key as string);
  };

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-label-fg">{t("label")}</Label>
      <Select
        className="min-w-[398px]"
        selectedKey={titleModelToggle}
        onSelectionChange={handleTitleModelToggleChange}
        aria-label="Select title model toggle"
      >
        <Select.Trigger className="inset-ring-transparent h-11 rounded-[10px] bg-setting text-setting-fg transition-none hover:inset-ring-transparent" />
        <Select.List
          popover={{ className: "min-w-[398px]" }}
          items={titleModelToggles}
        >
          {({ key, label }) => (
            <Select.Option
              className={cn(
                "flex cursor-pointer justify-between",
                "[&>[data-slot='check-indicator']]:order-last [&>[data-slot='check-indicator']]:mr-0 [&>[data-slot='check-indicator']]:ml-auto",
              )}
              key={key}
              id={key}
              textValue={label}
            >
              <span className="flex items-center gap-2">
                <span className="text-base">{label}</span>
              </span>
            </Select.Option>
          )}
        </Select.List>
      </Select>
    </div>
  );
}
