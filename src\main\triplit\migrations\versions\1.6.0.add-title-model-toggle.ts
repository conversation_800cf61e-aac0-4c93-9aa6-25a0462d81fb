/** biome-ignore-all lint/suspicious/noExplicitAny: ignore all */
import type {
  CollectionMigrationConfig,
  DataFillFunction,
  MigrationContext,
} from "@shared/triplit/migrations/types";
import { FieldMigration } from "@shared/triplit/migrations/base-migration";

export class AddTitleModelToggleMigration extends FieldMigration {
  public readonly version = "1.6.0";
  public readonly description = "Add title model toggle setting (titleModelToggle) to settings collection";
  public readonly dependencies: string[] = [];

  private fillTitleModelToggleSettings: DataFillFunction = async (
    _client,
    entity,
    context,
  ) => {
    const { logger } = context;

    // Set default value for title model toggle setting
    const titleModelToggle = "first-conversation";  // Default to generate title only for first conversation

    logger.debug(`Setting default title model toggle setting for settings entity ${entity.id}`);

    return {
      titleModelToggle,
    };
  };

  protected getMigrationConfig(): CollectionMigrationConfig[] {
    return [
      {
        collectionName: "settings",
        fields: [
          {
            fieldName: "titleModelToggle",
            fillFunction: this.fillTitleModelToggleSettings,
          },
        ],
        customMigration: async (context: MigrationContext) => {
          const { logger } = context;
          logger.info("Running custom migration logic for title model toggle setting");
          logger.info("Title generation will be enabled for first conversation by default");
        },
      },
    ];
  }
}
